package com.example.linkeye;

import android.content.Context;
import android.content.res.Resources;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

public class CameraRepository {
	public static List<CameraInfo> load(Context context) {
		List<CameraInfo> list = new ArrayList<>();
		try {
			Resources res = context.getResources();
			int resourceId = res.getIdentifier("cameras", "raw", context.getPackageName());
			if (resourceId == 0) return list;
			InputStream is = res.openRawResource(resourceId);
			BufferedReader br = new BufferedReader(new InputStreamReader(is));
			StringBuilder sb = new StringBuilder();
			String line;
			while ((line = br.readLine()) != null) sb.append(line);
			br.close();
			JSONArray arr = new JSONArray(sb.toString());
			for (int i = 0; i < arr.length(); i++) {
				JSONObject o = arr.getJSONObject(i);
				String id = o.optString("id");
				String name = o.optString("name");
				String url = o.optString("url");
				if (id != null && !id.isEmpty() && name != null && url != null && !url.isEmpty()) {
					list.add(new CameraInfo(id, name, url));
				}
			}
		} catch (Throwable t) {
			// ignore and return what we have
		}
		return list;
	}
}

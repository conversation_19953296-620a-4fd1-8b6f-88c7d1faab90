package com.example.linkeye;

import java.util.List;

public class TvChannel extends CameraInfo {
    public final String tvgId;
    public final String tvgLogo;
    public final String groupTitle;
    public final List<String> sources; // Multiple sources for the same channel
    private int currentSourceIndex = 0;

    public TvChannel(String name, String tvgId, String tvgLogo, String groupTitle, List<String> sources) {
        super(tvgId != null ? tvgId : name, name, sources.isEmpty() ? "" : sources.get(0));
        this.tvgId = tvgId;
        this.tvgLogo = tvgLogo;
        this.groupTitle = groupTitle;
        this.sources = sources;
    }

    @Override
    public boolean isTvChannel() {
        return true;
    }

    public String getCurrentSource() {
        if (sources.isEmpty()) return "";
        if (currentSourceIndex >= sources.size()) currentSourceIndex = 0;
        return sources.get(currentSourceIndex);
    }

    public boolean hasNextSource() {
        return sources.size() > 1 && currentSourceIndex < sources.size() - 1;
    }

    public String getNextSource() {
        if (sources.isEmpty()) return "";
        currentSourceIndex = (currentSourceIndex + 1) % sources.size();
        return getCurrentSource();
    }

    public void resetSourceIndex() {
        currentSourceIndex = 0;
    }

    public int getSourceCount() {
        return sources.size();
    }

    public int getCurrentSourceIndex() {
        return currentSourceIndex;
    }
}

@echo off
setlocal enabledelayedexpansion

echo ========================================
echo LinkEye Version Update Script
echo ========================================

if "%1"=="" (
    echo Usage: update-version.bat [VERSION_NAME] [VERSION_CODE]
    echo Example: update-version.bat 1.1.0 2
    echo.
    echo Current version:
    type app\version.properties | findstr VERSION
    pause
    exit /b 1
)

set NEW_VERSION_NAME=%1
set NEW_VERSION_CODE=%2

if "%NEW_VERSION_CODE%"=="" (
    echo Error: Both VERSION_NAME and VERSION_CODE are required
    echo Usage: update-version.bat [VERSION_NAME] [VERSION_CODE]
    pause
    exit /b 1
)

echo.
echo Updating version to %NEW_VERSION_NAME% (code: %NEW_VERSION_CODE%)

echo # LinkEye版本配置文件 > app\version.properties
echo # 每次发布新版本时更新这些值 >> app\version.properties
echo. >> app\version.properties
echo VERSION_CODE=%NEW_VERSION_CODE% >> app\version.properties
echo VERSION_NAME=%NEW_VERSION_NAME% >> app\version.properties
echo. >> app\version.properties
echo # 版本历史: >> app\version.properties
echo # %NEW_VERSION_NAME% (%NEW_VERSION_CODE%) - Updated on %date% >> app\version.properties

echo.
echo Version updated successfully!
echo New version: %NEW_VERSION_NAME% (code: %NEW_VERSION_CODE%)
echo.
echo You can now run build-release.bat to create a new release APK.

pause
